# 特殊情况说明模块

## 功能概述

特殊情况说明模块允许管理员查看员工为订单添加的特殊情况说明，包括文字描述和相关图片。

## 功能特性

### 1. 查看特殊情况说明
- 支持查看订单的特殊情况说明详情
- 显示员工信息、创建时间
- 展示文字内容和相关图片
- 支持图片预览功能

### 2. 集成位置
- **状态看板**: 在订单卡片的操作按钮中添加"特殊情况"按钮
- **详细列表**: 在表格操作列中添加"特殊情况"链接
- **订单详情**: 在订单详情弹窗中添加"查看特殊情况"按钮

### 3. 显示条件
特殊情况说明按钮仅在以下订单状态时显示：
- 服务中
- 已完成
- 已评价

## 技术实现

### API接口
- `GET /openapi/order-special-notes/:orderId` - 查询订单特殊情况说明

### 组件结构
```
src/pages/Appointment/
├── SpecialNotesModal.tsx          # 特殊情况说明查看弹窗
├── components/
│   └── StatusBoardView.tsx        # 状态看板（已更新）
├── index.tsx                      # 预约管理主页面（已更新）
└── DetailModal.tsx                # 订单详情弹窗（已更新）

src/services/
├── order-special-notes.ts         # 特殊情况说明API服务
└── typings.d.ts                   # 类型定义（已更新）
```

### 类型定义
```typescript
interface OrderSpecialNote extends IResponseDate {
  id: number;                      // 特殊情况说明ID
  orderId: number;                 // 关联订单ID
  employeeId: number;              // 员工ID
  content: string;                 // 特殊情况说明内容
  photos: string[];                // 特殊情况图片URL数组
  order?: Order;                   // 关联的订单信息
  employee?: Employee;             // 关联的员工信息
}
```

## 使用说明

1. **在状态看板中查看**
   - 进入预约管理 → 状态看板
   - 找到状态为"服务中"、"已完成"或"已评价"的订单
   - 点击订单卡片中的"特殊情况"按钮

2. **在详细列表中查看**
   - 进入预约管理 → 详细列表
   - 在操作列中点击"特殊情况"链接

3. **在订单详情中查看**
   - 打开任意订单的详情弹窗
   - 在订单信息的操作行中点击"查看特殊情况"按钮

## 注意事项

- 如果订单没有特殊情况说明，弹窗会显示"该订单暂无特殊情况说明"
- 图片支持点击预览功能
- 特殊情况说明内容支持换行显示
- 仅在员工端有权限添加特殊情况说明，管理端只能查看
